import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/centaur_writing_controller.dart';

/// 半人马写作主界面
/// 超越人类，超越套路的AI创作思维链
class CentaurWritingScreen extends StatelessWidget {
  const CentaurWritingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CentaurWritingController());
    
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.psychology, color: Colors.purple),
            SizedBox(width: 8),
            Text('半人马写作'),
          ],
        ),
        backgroundColor: Colors.purple.shade50,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.resetAll,
            tooltip: '重置所有',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
            tooltip: '帮助',
          ),
        ],
      ),
      body: Column(
        children: [
          // 顶部标语
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.purple.shade50, Colors.white],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: const Column(
              children: [
                Text(
                  '超越人类，超越套路',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'AI创作思维链 · 爽文生成引擎',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          
          // 主要内容区域
          Expanded(
            child: Row(
              children: [
                // 左侧设置面板
                Container(
                  width: 320,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    border: Border(
                      right: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: _buildSettingsPanel(controller),
                ),
                
                // 右侧内容显示区域
                Expanded(
                  child: _buildContentArea(controller),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建设置面板
  Widget _buildSettingsPanel(CentaurWritingController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 创作模式选择
          _buildModeSelector(controller),
          const SizedBox(height: 20),
          
          // 基本设置
          _buildBasicSettings(controller),
          const SizedBox(height: 20),
          
          // 类型选择
          _buildGenreSelector(controller),
          const SizedBox(height: 20),
          
          // 操作按钮
          _buildActionButtons(controller),
        ],
      ),
    );
  }

  /// 构建模式选择器
  Widget _buildModeSelector(CentaurWritingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '创作模式',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Obx(() => Column(
          children: controller.writingModes.map((mode) {
            final isSelected = controller.currentMode.value == mode['key'];
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: InkWell(
                onTap: () => controller.switchMode(mode['key']),
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isSelected ? mode['color'].withOpacity(0.1) : Colors.white,
                    border: Border.all(
                      color: isSelected ? mode['color'] : Colors.grey.shade300,
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        mode['icon'],
                        color: isSelected ? mode['color'] : Colors.grey,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              mode['title'],
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: isSelected ? mode['color'] : Colors.black87,
                              ),
                            ),
                            Text(
                              mode['subtitle'],
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        )),
      ],
    );
  }

  /// 构建基本设置
  Widget _buildBasicSettings(CentaurWritingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '基本设置',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        
        // 小说标题
        TextField(
          controller: controller.novelTitleController,
          decoration: const InputDecoration(
            labelText: '小说标题',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.title),
          ),
        ),
        const SizedBox(height: 12),
        
        // 主题
        TextField(
          controller: controller.themeController,
          decoration: const InputDecoration(
            labelText: '小说主题',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.lightbulb_outline),
          ),
        ),
        const SizedBox(height: 12),
        
        // 背景设定
        TextField(
          controller: controller.backgroundController,
          maxLines: 3,
          decoration: const InputDecoration(
            labelText: '背景设定',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.landscape),
          ),
        ),
        const SizedBox(height: 12),
        
        // 目标读者
        TextField(
          controller: controller.targetReadersController,
          decoration: const InputDecoration(
            labelText: '目标读者',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.people),
          ),
        ),
        
        // 自定义提示（用于打脸情节等）
        Obx(() {
          if (controller.currentMode.value == 'face_slap') {
            return Column(
              children: [
                const SizedBox(height: 12),
                TextField(
                  controller: controller.customPromptController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: '当前情况描述',
                    hintText: '描述需要设计打脸情节的具体情况...',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                ),
              ],
            );
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  /// 构建类型选择器
  Widget _buildGenreSelector(CentaurWritingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '小说类型',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Obx(() => Wrap(
          spacing: 8,
          runSpacing: 8,
          children: controller.availableGenres.map((genre) {
            final isSelected = controller.selectedGenres.contains(genre);
            return FilterChip(
              label: Text(genre),
              selected: isSelected,
              onSelected: (selected) => controller.toggleGenre(genre),
              selectedColor: Colors.purple.withOpacity(0.2),
              checkmarkColor: Colors.purple,
            );
          }).toList(),
        )),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(CentaurWritingController controller) {
    return Obx(() {
      final currentModeInfo = controller.getCurrentModeInfo();
      
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 主要操作按钮
          ElevatedButton.icon(
            onPressed: controller.isGenerating.value ? null : () {
              switch (controller.currentMode.value) {
                case 'thinking_chain':
                  controller.executeThinkingChain();
                  break;
                case 'cool_novel':
                  controller.generateCoolNovel();
                  break;
                case 'anti_routine':
                  controller.analyzeAntiRoutine();
                  break;
                case 'golden_finger':
                  controller.designGoldenFinger();
                  break;
                case 'face_slap':
                  controller.designFaceSlap();
                  break;
              }
            },
            icon: controller.isGenerating.value 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Icon(currentModeInfo['icon']),
            label: Text(controller.isGenerating.value 
                ? '生成中...' 
                : currentModeInfo['title']),
            style: ElevatedButton.styleFrom(
              backgroundColor: currentModeInfo['color'],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // 继续创作按钮（仅在爽文模式下显示）
          if (controller.currentMode.value == 'cool_novel')
            ElevatedButton.icon(
              onPressed: controller.isGenerating.value || controller.contentHistory.isEmpty 
                  ? null 
                  : controller.continueWriting,
              icon: const Icon(Icons.play_arrow),
              label: const Text('继续创作'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          
          const SizedBox(height: 8),
          
          // 清空内容按钮
          OutlinedButton.icon(
            onPressed: controller.clearContent,
            icon: const Icon(Icons.clear),
            label: const Text('清空内容'),
          ),
        ],
      );
    });
  }

  /// 构建内容显示区域
  Widget _buildContentArea(CentaurWritingController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              Obx(() {
                final modeInfo = controller.getCurrentModeInfo();
                return Row(
                  children: [
                    Icon(modeInfo['icon'], color: modeInfo['color']),
                    const SizedBox(width: 8),
                    Text(
                      modeInfo['title'],
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                );
              }),
              const Spacer(),
              // 复制按钮
              Obx(() => controller.generatedContent.value.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: controller.generatedContent.value));
                        Get.snackbar('成功', '内容已复制到剪贴板');
                      },
                      tooltip: '复制内容',
                    )
                  : const SizedBox.shrink()),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 内容显示区域
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Obx(() {
                if (controller.isGenerating.value) {
                  return Column(
                    children: [
                      const LinearProgressIndicator(),
                      const SizedBox(height: 16),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            controller.currentOutput.value.isEmpty 
                                ? '正在生成中，请稍候...' 
                                : controller.currentOutput.value,
                            style: const TextStyle(fontSize: 14, height: 1.6),
                          ),
                        ),
                      ),
                    ],
                  );
                } else if (controller.generatedContent.value.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.psychology, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          '选择创作模式并填写设置，开始你的半人马创作之旅',
                          style: TextStyle(color: Colors.grey, fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                } else {
                  return SingleChildScrollView(
                    child: SelectableText(
                      controller.generatedContent.value,
                      style: const TextStyle(fontSize: 14, height: 1.6),
                    ),
                  );
                }
              }),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示帮助对话框
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help_outline, color: Colors.purple),
            SizedBox(width: 8),
            Text('半人马写作帮助'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '半人马写作是基于"超越人类，超越套路"理念的AI创作工具，具有以下特色：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text('🧠 思维链分析：AI深度分析创作逻辑和情节发展'),
              SizedBox(height: 8),
              Text('⚡ 爽文创作：基于思维链的高质量爽文生成'),
              SizedBox(height: 8),
              Text('💡 反套路分析：打破常见套路的创新方案'),
              SizedBox(height: 8),
              Text('⭐ 金手指设计：创新的能力系统设计'),
              SizedBox(height: 8),
              Text('🔥 打脸情节：经典打脸情节的精准设计'),
              SizedBox(height: 12),
              Text(
                '使用建议：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('1. 先进行思维链分析，了解AI的创作思路'),
              Text('2. 基于分析结果生成爽文内容'),
              Text('3. 使用继续创作功能实现持续创作'),
              Text('4. 利用其他模式辅助创作设计'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }
}
