import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/services/centaur_thinking_chain_service.dart';
import 'package:novel_app/models/novel.dart';

/// 半人马写作控制器
/// 管理AI创作思维链和持续创作功能
class CentaurWritingController extends GetxController {
  final CentaurThinkingChainService _thinkingChainService = Get.put(CentaurThinkingChainService());

  // UI控制器
  final TextEditingController novelTitleController = TextEditingController();
  final TextEditingController themeController = TextEditingController();
  final TextEditingController backgroundController = TextEditingController();
  final TextEditingController targetReadersController = TextEditingController();
  final TextEditingController customPromptController = TextEditingController();

  // 响应式状态
  final RxList<String> selectedGenres = <String>[].obs;
  final RxString currentMode = 'thinking_chain'.obs; // thinking_chain, cool_novel, anti_routine, golden_finger, face_slap
  final RxBool isGenerating = false.obs;
  final RxString generatedContent = ''.obs;
  final RxString thinkingChainResult = ''.obs;
  final RxString currentOutput = ''.obs;
  final RxList<String> contentHistory = <String>[].obs;
  final RxInt currentStep = 0.obs; // 0: 设置, 1: 思维链分析, 2: 内容生成

  // 可选类型
  final List<String> availableGenres = [
    '玄幻', '都市', '历史', '科幻', '武侠', '仙侠', '军事', '游戏',
    '竞技', '悬疑', '灵异', '二次元', '轻小说', '现实', '其他'
  ];

  // 创作模式选项
  final List<Map<String, dynamic>> writingModes = [
    {
      'key': 'thinking_chain',
      'title': '思维链分析',
      'subtitle': 'AI深度思考创作逻辑',
      'icon': Icons.psychology,
      'color': Colors.purple,
    },
    {
      'key': 'cool_novel',
      'title': '爽文创作',
      'subtitle': '基于思维链的爽文生成',
      'icon': Icons.auto_awesome,
      'color': Colors.orange,
    },
    {
      'key': 'anti_routine',
      'title': '反套路分析',
      'subtitle': '打破常见套路的创新方案',
      'icon': Icons.lightbulb,
      'color': Colors.green,
    },
    {
      'key': 'golden_finger',
      'title': '金手指设计',
      'subtitle': '创新的能力系统设计',
      'icon': Icons.star,
      'color': Colors.amber,
    },
    {
      'key': 'face_slap',
      'title': '打脸情节',
      'subtitle': '经典打脸情节设计',
      'icon': Icons.flash_on,
      'color': Colors.red,
    },
  ];

  @override
  void onInit() {
    super.onInit();
    // 初始化默认值
    targetReadersController.text = '网文读者';
    themeController.text = '成长与逆袭';
  }

  @override
  void onClose() {
    novelTitleController.dispose();
    themeController.dispose();
    backgroundController.dispose();
    targetReadersController.dispose();
    customPromptController.dispose();
    super.onClose();
  }

  /// 切换创作模式
  void switchMode(String mode) {
    currentMode.value = mode;
    generatedContent.value = '';
    currentOutput.value = '';
  }

  /// 切换类型选择
  void toggleGenre(String genre) {
    if (selectedGenres.contains(genre)) {
      selectedGenres.remove(genre);
    } else {
      selectedGenres.add(genre);
    }
  }

  /// 执行思维链分析
  Future<void> executeThinkingChain() async {
    if (!_validateInput()) return;

    try {
      isGenerating.value = true;
      currentStep.value = 1;
      currentOutput.value = '';

      final result = await _thinkingChainService.executeThinkingChain(
        task: '创作思维链分析',
        novelTitle: novelTitleController.text,
        genres: selectedGenres,
        theme: themeController.text,
        targetReaders: targetReadersController.text,
        background: backgroundController.text,
        previousContent: contentHistory.isNotEmpty ? contentHistory.last : '',
      );

      thinkingChainResult.value = result;
      generatedContent.value = result;
      currentOutput.value = result;

      Get.snackbar('成功', '思维链分析完成', backgroundColor: Colors.green.withOpacity(0.8));
      
    } catch (e) {
      Get.snackbar('错误', '思维链分析失败: $e', backgroundColor: Colors.red.withOpacity(0.8));
    } finally {
      isGenerating.value = false;
    }
  }

  /// 生成爽文内容
  Future<void> generateCoolNovel() async {
    if (!_validateInput()) return;

    // 如果没有思维链分析结果，先执行分析
    if (thinkingChainResult.value.isEmpty) {
      await executeThinkingChain();
      if (thinkingChainResult.value.isEmpty) return;
    }

    try {
      isGenerating.value = true;
      currentStep.value = 2;
      currentOutput.value = '';

      await _thinkingChainService.generateCoolNovelContent(
        task: '爽文内容生成',
        novelTitle: novelTitleController.text,
        genres: selectedGenres,
        theme: themeController.text,
        targetReaders: targetReadersController.text,
        background: backgroundController.text,
        thinkingChainAnalysis: thinkingChainResult.value,
        previousContent: contentHistory.isNotEmpty ? contentHistory.last : '',
        wordCount: 3000,
        onProgress: (chunk) {
          currentOutput.value += chunk;
        },
      );

      generatedContent.value = currentOutput.value;
      contentHistory.add(currentOutput.value);

      Get.snackbar('成功', '爽文内容生成完成', backgroundColor: Colors.green.withOpacity(0.8));
      
    } catch (e) {
      Get.snackbar('错误', '爽文内容生成失败: $e', backgroundColor: Colors.red.withOpacity(0.8));
    } finally {
      isGenerating.value = false;
    }
  }

  /// 分析反套路
  Future<void> analyzeAntiRoutine() async {
    if (selectedGenres.isEmpty) {
      Get.snackbar('提示', '请先选择小说类型', backgroundColor: Colors.orange.withOpacity(0.8));
      return;
    }

    try {
      isGenerating.value = true;
      currentOutput.value = '';

      final result = await _thinkingChainService.analyzeAntiRoutine(
        targetGenre: selectedGenres.first,
      );

      generatedContent.value = result;
      currentOutput.value = result;

      Get.snackbar('成功', '反套路分析完成', backgroundColor: Colors.green.withOpacity(0.8));
      
    } catch (e) {
      Get.snackbar('错误', '反套路分析失败: $e', backgroundColor: Colors.red.withOpacity(0.8));
    } finally {
      isGenerating.value = false;
    }
  }

  /// 设计金手指系统
  Future<void> designGoldenFinger() async {
    if (!_validateInput()) return;

    try {
      isGenerating.value = true;
      currentOutput.value = '';

      final result = await _thinkingChainService.designGoldenFingerSystem(
        novelTitle: novelTitleController.text,
        genres: selectedGenres,
        theme: themeController.text,
        background: backgroundController.text,
      );

      generatedContent.value = result;
      currentOutput.value = result;

      Get.snackbar('成功', '金手指系统设计完成', backgroundColor: Colors.green.withOpacity(0.8));
      
    } catch (e) {
      Get.snackbar('错误', '金手指系统设计失败: $e', backgroundColor: Colors.red.withOpacity(0.8));
    } finally {
      isGenerating.value = false;
    }
  }

  /// 设计打脸情节
  Future<void> designFaceSlap() async {
    if (novelTitleController.text.isEmpty || customPromptController.text.isEmpty) {
      Get.snackbar('提示', '请填写小说标题和当前情况描述', backgroundColor: Colors.orange.withOpacity(0.8));
      return;
    }

    try {
      isGenerating.value = true;
      currentOutput.value = '';

      final result = await _thinkingChainService.designFaceSlapPlot(
        novelTitle: novelTitleController.text,
        currentSituation: customPromptController.text,
        protagonistStatus: '待分析',
        antagonist: '待分析',
        environment: '待分析',
      );

      generatedContent.value = result;
      currentOutput.value = result;

      Get.snackbar('成功', '打脸情节设计完成', backgroundColor: Colors.green.withOpacity(0.8));
      
    } catch (e) {
      Get.snackbar('错误', '打脸情节设计失败: $e', backgroundColor: Colors.red.withOpacity(0.8));
    } finally {
      isGenerating.value = false;
    }
  }

  /// 继续创作
  Future<void> continueWriting() async {
    if (contentHistory.isEmpty) {
      Get.snackbar('提示', '没有可继续的内容', backgroundColor: Colors.orange.withOpacity(0.8));
      return;
    }

    await generateCoolNovel();
  }

  /// 清空内容
  void clearContent() {
    generatedContent.value = '';
    currentOutput.value = '';
    thinkingChainResult.value = '';
    contentHistory.clear();
    currentStep.value = 0;
  }

  /// 重置所有设置
  void resetAll() {
    novelTitleController.clear();
    themeController.clear();
    backgroundController.clear();
    targetReadersController.text = '网文读者';
    customPromptController.clear();
    selectedGenres.clear();
    clearContent();
  }

  /// 验证输入
  bool _validateInput() {
    if (novelTitleController.text.isEmpty) {
      Get.snackbar('提示', '请输入小说标题', backgroundColor: Colors.orange.withOpacity(0.8));
      return false;
    }
    if (selectedGenres.isEmpty) {
      Get.snackbar('提示', '请选择至少一个小说类型', backgroundColor: Colors.orange.withOpacity(0.8));
      return false;
    }
    if (themeController.text.isEmpty) {
      Get.snackbar('提示', '请输入小说主题', backgroundColor: Colors.orange.withOpacity(0.8));
      return false;
    }
    return true;
  }

  /// 获取当前模式信息
  Map<String, dynamic> getCurrentModeInfo() {
    return writingModes.firstWhere(
      (mode) => mode['key'] == currentMode.value,
      orElse: () => writingModes.first,
    );
  }
}
