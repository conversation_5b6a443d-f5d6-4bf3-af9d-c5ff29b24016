import 'package:langchain/langchain.dart';

/// 半人马写作的提示词模板集合
/// 基于"超越人类，超越套路"的创作理念
class CentaurWritingPrompts {
  /// 创作思维链分析提示模板
  static final PromptTemplate thinkingChainTemplate = PromptTemplate.fromTemplate('''
你是一位具有超人级创作能力的半人马AI作家，拥有独特的创作思维链。请使用中文回复。

**当前创作任务：**
{task}

**小说基本信息：**
- 标题：{novelTitle}
- 类型：{genres}
- 主题：{theme}
- 目标读者：{targetReaders}
- 背景设定：{background}

**前文内容：**
{previousContent}

**创作思维链分析：**
请按照以下步骤进行深度思考分析，每个步骤都要详细展开：

### 1. 情节逻辑分析
- 分析当前情节的发展脉络
- 识别潜在的冲突点和转折机会
- 评估情节的"爽点"密度和节奏

### 2. 人物发展轨迹
- 分析主要角色的心理状态和动机
- 预测角色行为的合理性和意外性
- 设计角色成长或堕落的关键节点

### 3. 爽文元素设计
- 识别可以植入的"打脸"机会
- 设计"反转"的逻辑基础和情感冲击
- 规划"金手指"的使用时机和限制

### 4. 读者情绪调控
- 分析当前读者的情绪状态
- 设计情绪的"紧张-释放"循环
- 预测读者的期待和如何超越期待

### 5. 创新突破点
- 识别可能的套路陷阱
- 寻找"反套路"的创新机会
- 融合不同类型元素的可能性

**输出要求：**
请详细输出你的思维链分析过程，每个步骤都要有具体的分析和建议。这将作为后续创作的指导依据。
''');

  /// 爽文创作提示模板
  static final PromptTemplate coolNovelTemplate = PromptTemplate.fromTemplate('''
你是一位专精爽文创作的半人马AI作家，请使用中文创作。

**创作任务：**
{task}

**小说信息：**
- 标题：{novelTitle}
- 类型：{genres}
- 主题：{theme}
- 目标读者：{targetReaders}
- 背景设定：{background}

**思维链分析结果：**
{thinkingChainAnalysis}

**前文内容：**
{previousContent}

**创作指导原则：**

1. **效价与密度优化**
   - 每1000字至少包含1个明显的"爽点"
   - 使用"断言-潜藏-揭示-升格"的打脸结构
   - 确保每个反转都有充分的因果铺垫

2. **叙事复杂性管理**
   - 维持多条情节线的内在一致性
   - 为未来的反转预埋伏笔
   - 构建复杂但清晰的因果关系网

3. **创造性革新**
   - 避免常见的爽文套路
   - 融合不同类型的创新元素
   - 创造出人意料但合理的情节发展

4. **情感工程化**
   - 精确控制读者的情绪曲线
   - 从绝望到希望的情感转换
   - 营造压倒性的满足感

**具体创作要求：**
- 字数：约{wordCount}字
- 直接开始创作，不要添加标题或章节标记
- 重点突出爽文元素的运用
- 保持与前文的连贯性
- 为后续情节发展留下伏笔

请现在开始创作，展现你的超人级爽文创作能力。
''');

  /// 反套路创新提示模板
  static final PromptTemplate antiRoutineTemplate = PromptTemplate.fromTemplate('''
你是一位专门打破套路的半人马AI创新作家，请使用中文回复。

**分析任务：**
请分析以下爽文类型的常见套路，并提供创新的反套路方案。

**目标类型：**
{targetGenre}

**常见套路分析：**
请列出该类型最常见的5个陈词滥调套路，并为每个套路提供3个颠覆性的创新方案。

**创新要求：**
1. 保持故事的可读性和吸引力
2. 创新要有逻辑基础，不能为了反套路而反套路
3. 要能激发读者的新鲜感和惊喜感
4. 考虑与其他类型元素的融合可能

**输出格式：**
### 套路1：[套路名称]
**常见表现：**[描述]
**创新方案：**
1. [方案1]
2. [方案2] 
3. [方案3]

请按此格式分析5个套路并提供创新方案。
''');

  /// 金手指系统设计提示模板
  static final PromptTemplate goldenFingerTemplate = PromptTemplate.fromTemplate('''
你是一位专业的金手指系统设计师，请使用中文回复。

**设计任务：**
为以下小说设计一个创新的金手指系统。

**小说信息：**
- 标题：{novelTitle}
- 类型：{genres}
- 主题：{theme}
- 背景：{background}

**设计要求：**
请设计一个完整的金手指系统，包含以下要素：

### 1. 核心机制
- 金手指的基本原理和运作方式
- 与世界观的融合度
- 独特性和创新性

### 2. 获取条件
- 主角如何获得这个金手指
- 获得的过程和代价
- 初始状态和限制

### 3. 升级路径
- 明确的升级条件和路径
- 每个阶段的能力变化
- 升级所需的资源或行动

### 4. 使用代价
- 使用金手指的限制和风险
- 道德或身体上的代价
- 防止过度强大的平衡机制

### 5. 终极形态
- 金手指的最终进化状态
- 达到终极形态的条件
- 终极能力的描述

**创新要求：**
- 避免常见的升级流、系统流套路
- 融合科学原理或独特概念
- 确保系统本身就是故事的驱动力

请详细设计这个金手指系统。
''');

  /// 人物打脸情节设计提示模板
  static final PromptTemplate faceSlapTemplate = PromptTemplate.fromTemplate('''
你是一位专精打脸情节的半人马AI编剧，请使用中文回复。

**情节设计任务：**
为以下场景设计一个高质量的打脸情节。

**场景信息：**
- 小说：{novelTitle}
- 当前情况：{currentSituation}
- 主角状态：{protagonistStatus}
- 反派角色：{antagonist}
- 场景环境：{environment}

**打脸结构设计：**
请按照经典的四步打脸结构进行设计：

### 1. 断言阶段
- 反派的傲慢断言或羞辱内容
- 断言的具体表现和语言
- 周围人的反应和态度

### 2. 潜藏阶段  
- 主角的表面反应（平静/不屑）
- 主角隐藏实力的暗示
- 为后续反转埋下的伏笔

### 3. 揭示阶段
- 真相揭露的时机和方式
- 主角实力展现的具体表现
- 证明反派断言错误的证据

### 4. 升格阶段
- 反派遭受的羞辱和后果
- 主角地位的提升表现
- 围观者态度的转变

**创作要求：**
- 确保打脸有充分的逻辑基础
- 避免突兀和无意义的反转
- 营造强烈的情感冲击和满足感
- 为后续情节发展做好铺垫

请详细设计这个打脸情节的完整过程。
''');
}
