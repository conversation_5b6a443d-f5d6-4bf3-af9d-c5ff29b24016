import 'dart:convert';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/langchain/prompts/centaur_writing_prompts.dart';

/// 半人马写作思维链服务
/// 实现AI的创作思维过程和决策链
class CentaurThinkingChainService extends GetxService {
  final ApiConfigController _apiConfigController =
      Get.find<ApiConfigController>();

  /// 执行创作思维链分析
  Future<String> executeThinkingChain({
    required String task,
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required String background,
    String previousContent = '',
  }) async {
    try {
      print('[CentaurThinkingChain] 开始执行创作思维链分析...');

      // 检查API配置
      final modelConfig = _apiConfigController.getCurrentModel();
      print('[CentaurThinkingChain] 使用模型: ${modelConfig.model}');
      print('[CentaurThinkingChain] API格式: ${modelConfig.apiFormat}');
      print('[CentaurThinkingChain] API URL: ${modelConfig.apiUrl}');

      // 准备思维链分析的输入参数
      final input = {
        'task': task,
        'novelTitle': novelTitle,
        'genres': genres.join(', '),
        'theme': theme,
        'targetReaders': targetReaders,
        'background': background,
        'previousContent':
            previousContent.isNotEmpty ? previousContent : '暂无前文内容',
      };

      // 格式化提示词
      final prompt = CentaurWritingPrompts.thinkingChainTemplate.format(input);
      print('[CentaurThinkingChain] 提示词长度: ${prompt.length}');

      // 发送请求到AI模型
      final response = await _sendAIRequest(prompt);

      print('[CentaurThinkingChain] 思维链分析完成，长度: ${response.length}');
      return response;
    } catch (e) {
      print('[CentaurThinkingChain] 思维链分析失败: $e');
      rethrow; // 重新抛出原始异常，保留堆栈信息
    }
  }

  /// 基于思维链分析结果生成爽文内容
  Future<String> generateCoolNovelContent({
    required String task,
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required String background,
    required String thinkingChainAnalysis,
    String previousContent = '',
    int wordCount = 3000,
    void Function(String)? onProgress,
  }) async {
    try {
      print('[CentaurThinkingChain] 开始基于思维链生成爽文内容...');

      // 准备创作输入参数
      final input = {
        'task': task,
        'novelTitle': novelTitle,
        'genres': genres.join(', '),
        'theme': theme,
        'targetReaders': targetReaders,
        'background': background,
        'thinkingChainAnalysis': thinkingChainAnalysis,
        'previousContent':
            previousContent.isNotEmpty ? previousContent : '暂无前文内容',
        'wordCount': wordCount.toString(),
      };

      // 格式化提示词
      final prompt = CentaurWritingPrompts.coolNovelTemplate.format(input);

      // 发送流式请求
      if (onProgress != null) {
        return await _sendStreamAIRequest(prompt, onProgress);
      } else {
        return await _sendAIRequest(prompt);
      }
    } catch (e) {
      print('[CentaurThinkingChain] 爽文内容生成失败: $e');
      throw Exception('爽文内容生成失败: $e');
    }
  }

  /// 分析并提供反套路创新方案
  Future<String> analyzeAntiRoutine({
    required String targetGenre,
  }) async {
    try {
      print('[CentaurThinkingChain] 开始反套路分析...');

      final input = {
        'targetGenre': targetGenre,
      };

      final prompt = CentaurWritingPrompts.antiRoutineTemplate.format(input);
      final response = await _sendAIRequest(prompt);

      print('[CentaurThinkingChain] 反套路分析完成');
      return response;
    } catch (e) {
      print('[CentaurThinkingChain] 反套路分析失败: $e');
      throw Exception('反套路分析失败: $e');
    }
  }

  /// 设计金手指系统
  Future<String> designGoldenFingerSystem({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String background,
  }) async {
    try {
      print('[CentaurThinkingChain] 开始设计金手指系统...');

      final input = {
        'novelTitle': novelTitle,
        'genres': genres.join(', '),
        'theme': theme,
        'background': background,
      };

      final prompt = CentaurWritingPrompts.goldenFingerTemplate.format(input);
      final response = await _sendAIRequest(prompt);

      print('[CentaurThinkingChain] 金手指系统设计完成');
      return response;
    } catch (e) {
      print('[CentaurThinkingChain] 金手指系统设计失败: $e');
      throw Exception('金手指系统设计失败: $e');
    }
  }

  /// 设计打脸情节
  Future<String> designFaceSlapPlot({
    required String novelTitle,
    required String currentSituation,
    required String protagonistStatus,
    required String antagonist,
    required String environment,
  }) async {
    try {
      print('[CentaurThinkingChain] 开始设计打脸情节...');

      final input = {
        'novelTitle': novelTitle,
        'currentSituation': currentSituation,
        'protagonistStatus': protagonistStatus,
        'antagonist': antagonist,
        'environment': environment,
      };

      final prompt = CentaurWritingPrompts.faceSlapTemplate.format(input);
      final response = await _sendAIRequest(prompt);

      print('[CentaurThinkingChain] 打脸情节设计完成');
      return response;
    } catch (e) {
      print('[CentaurThinkingChain] 打脸情节设计失败: $e');
      throw Exception('打脸情节设计失败: $e');
    }
  }

  /// 发送AI请求（普通模式）
  Future<String> _sendAIRequest(String prompt) async {
    final modelConfig = _apiConfigController.getCurrentModel();

    // 检查是否是阿里云通义千问模型
    bool isAliyunQwen = modelConfig.apiUrl.contains('dashscope.aliyuncs.com') ||
        modelConfig.name.contains('阿里') ||
        modelConfig.name.contains('通义');

    // 如果是阿里云通义千问，强制使用流式输出
    if (isAliyunQwen) {
      print('[CentaurThinkingChain] 检测到阿里云通义千问模型，使用流式输出');
      return await _sendAliyunStreamRequest(prompt);
    }

    // 构建完整的API URL
    String fullApiUrl = modelConfig.apiUrl;
    if (modelConfig.apiPath.isNotEmpty &&
        !modelConfig.apiUrl.endsWith(modelConfig.apiPath)) {
      if (!fullApiUrl.endsWith('/') && !modelConfig.apiPath.startsWith('/')) {
        fullApiUrl += '/';
      }
      fullApiUrl += modelConfig.apiPath;
    }

    // 准备请求数据
    final Map<String, dynamic> requestData = {};

    // 根据API格式构建请求
    if (modelConfig.apiFormat == 'Google API') {
      // Google API格式
      requestData.addAll({
        'contents': [
          {
            'parts': [
              {'text': prompt}
            ]
          }
        ],
        'generationConfig': {
          'temperature': 0.8,
          'topP': 0.9,
          'maxOutputTokens': 4000,
        }
      });

      // 添加API Key到URL
      if (modelConfig.apiKey.isNotEmpty) {
        fullApiUrl += '?key=${modelConfig.apiKey}';
      }
    } else {
      // OpenAI兼容格式
      requestData.addAll({
        'model': modelConfig.model,
        'messages': [
          {
            'role': 'user',
            'content': prompt,
          }
        ],
        'max_tokens': 4000,
        'temperature': 0.8,
        'top_p': 0.9,
      });
    }

    // 准备请求头
    final Map<String, String> headers = {
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'application/json; charset=utf-8',
    };

    // 添加认证头（非Google API）
    if (modelConfig.apiFormat != 'Google API' &&
        modelConfig.apiKey.isNotEmpty) {
      headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
    }

    try {
      // 发送请求
      final response = await http
          .post(
            Uri.parse(fullApiUrl),
            headers: headers,
            body: utf8.encode(jsonEncode(requestData)),
          )
          .timeout(const Duration(seconds: 120));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(utf8.decode(response.bodyBytes));

        // 根据API格式解析响应
        if (modelConfig.apiFormat == 'Google API') {
          return responseData['candidates'][0]['content']['parts'][0]['text']
              as String;
        } else {
          return responseData['choices'][0]['message']['content'] as String;
        }
      } else {
        print('[CentaurThinkingChain] API请求失败: ${response.statusCode}');
        print('[CentaurThinkingChain] 响应内容: ${response.body}');
        throw Exception('API请求失败: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('[CentaurThinkingChain] 请求异常: $e');
      rethrow;
    }
  }

  /// 发送阿里云通义千问流式请求
  Future<String> _sendAliyunStreamRequest(String prompt) async {
    final modelConfig = _apiConfigController.getCurrentModel();

    // 构建完整的API URL
    String fullApiUrl = modelConfig.apiUrl;
    if (modelConfig.apiPath.isNotEmpty &&
        !modelConfig.apiUrl.endsWith(modelConfig.apiPath)) {
      if (!fullApiUrl.endsWith('/') && !modelConfig.apiPath.startsWith('/')) {
        fullApiUrl += '/';
      }
      fullApiUrl += modelConfig.apiPath;
    }

    // 准备请求数据
    final Map<String, dynamic> requestData = {
      'model': modelConfig.model,
      'messages': [
        {
          'role': 'user',
          'content': prompt,
        }
      ],
      'max_tokens': 4000,
      'temperature': 0.8,
      'top_p': 0.9,
      'stream': true,
    };

    // 准备请求头
    final Map<String, String> headers = {
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'text/event-stream',
      'Authorization': 'Bearer ${modelConfig.apiKey}',
    };

    try {
      // 发送流式请求
      final request = http.Request('POST', Uri.parse(fullApiUrl));
      request.headers.addAll(headers);
      request.body = jsonEncode(requestData);

      final streamedResponse = await request.send();

      if (streamedResponse.statusCode != 200) {
        final responseBody = await streamedResponse.stream.bytesToString();
        throw Exception(
            '流式请求失败: ${streamedResponse.statusCode} - $responseBody');
      }

      final buffer = StringBuffer();

      await for (final chunk
          in streamedResponse.stream.transform(utf8.decoder)) {
        final lines = chunk.split('\n');

        for (final line in lines) {
          if (line.startsWith('data: ') && line != 'data: [DONE]') {
            try {
              final jsonStr = line.substring(6);
              final data = jsonDecode(jsonStr);

              if (data['choices'] != null && data['choices'].isNotEmpty) {
                final delta = data['choices'][0]['delta'];
                if (delta != null && delta['content'] != null) {
                  final content = delta['content'] as String;
                  buffer.write(content);
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }

      return buffer.toString();
    } catch (e) {
      print('[CentaurThinkingChain] 阿里云流式请求异常: $e');
      rethrow;
    }
  }

  /// 发送普通AI请求（不检查阿里云，避免循环调用）
  Future<String> _sendNormalAIRequest(String prompt) async {
    final modelConfig = _apiConfigController.getCurrentModel();

    // 构建完整的API URL
    String fullApiUrl = modelConfig.apiUrl;
    if (modelConfig.apiPath.isNotEmpty &&
        !modelConfig.apiUrl.endsWith(modelConfig.apiPath)) {
      if (!fullApiUrl.endsWith('/') && !modelConfig.apiPath.startsWith('/')) {
        fullApiUrl += '/';
      }
      fullApiUrl += modelConfig.apiPath;
    }

    // 准备请求数据
    final Map<String, dynamic> requestData = {};

    // 根据API格式构建请求
    if (modelConfig.apiFormat == 'Google API') {
      // Google API格式
      requestData.addAll({
        'contents': [
          {
            'parts': [
              {'text': prompt}
            ]
          }
        ],
        'generationConfig': {
          'temperature': 0.8,
          'topP': 0.9,
          'maxOutputTokens': 4000,
        }
      });

      // 添加API Key到URL
      if (modelConfig.apiKey.isNotEmpty) {
        fullApiUrl += '?key=${modelConfig.apiKey}';
      }
    } else {
      // OpenAI兼容格式
      requestData.addAll({
        'model': modelConfig.model,
        'messages': [
          {
            'role': 'user',
            'content': prompt,
          }
        ],
        'max_tokens': 4000,
        'temperature': 0.8,
        'top_p': 0.9,
      });
    }

    // 准备请求头
    final Map<String, String> headers = {
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'application/json; charset=utf-8',
    };

    // 添加认证头（非Google API）
    if (modelConfig.apiFormat != 'Google API' &&
        modelConfig.apiKey.isNotEmpty) {
      headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
    }

    try {
      // 发送请求
      final response = await http
          .post(
            Uri.parse(fullApiUrl),
            headers: headers,
            body: utf8.encode(jsonEncode(requestData)),
          )
          .timeout(const Duration(seconds: 120));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(utf8.decode(response.bodyBytes));

        // 根据API格式解析响应
        if (modelConfig.apiFormat == 'Google API') {
          return responseData['candidates'][0]['content']['parts'][0]['text']
              as String;
        } else {
          return responseData['choices'][0]['message']['content'] as String;
        }
      } else {
        throw Exception('API请求失败: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// 发送AI请求（流式模式）
  Future<String> _sendStreamAIRequest(
      String prompt, void Function(String) onProgress) async {
    final modelConfig = _apiConfigController.getCurrentModel();

    // 检查是否支持流式输出
    if (modelConfig.apiFormat == 'Google API') {
      // Google API不支持流式输出，使用普通请求（避免循环调用）
      final result = await _sendNormalAIRequest(prompt);
      onProgress(result);
      return result;
    }

    // 构建完整的API URL
    String fullApiUrl = modelConfig.apiUrl;
    if (modelConfig.apiPath.isNotEmpty &&
        !modelConfig.apiUrl.endsWith(modelConfig.apiPath)) {
      if (!fullApiUrl.endsWith('/') && !modelConfig.apiPath.startsWith('/')) {
        fullApiUrl += '/';
      }
      fullApiUrl += modelConfig.apiPath;
    }

    // 准备请求数据
    final Map<String, dynamic> requestData = {
      'model': modelConfig.model,
      'messages': [
        {
          'role': 'user',
          'content': prompt,
        }
      ],
      'max_tokens': 4000,
      'temperature': 0.8,
      'top_p': 0.9,
      'stream': true,
    };

    // 准备请求头
    final Map<String, String> headers = {
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'text/event-stream',
    };

    // 添加认证头
    if (modelConfig.apiKey.isNotEmpty) {
      headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
    }

    // 发送流式请求
    final request = http.Request('POST', Uri.parse(fullApiUrl));
    request.headers.addAll(headers);
    request.body = jsonEncode(requestData);

    final streamedResponse = await request.send();

    if (streamedResponse.statusCode != 200) {
      throw Exception('流式请求失败: ${streamedResponse.statusCode}');
    }

    final buffer = StringBuffer();

    await for (final chunk in streamedResponse.stream.transform(utf8.decoder)) {
      final lines = chunk.split('\n');

      for (final line in lines) {
        if (line.startsWith('data: ') && line != 'data: [DONE]') {
          try {
            final jsonStr = line.substring(6);
            final data = jsonDecode(jsonStr);

            if (data['choices'] != null && data['choices'].isNotEmpty) {
              final delta = data['choices'][0]['delta'];
              if (delta != null && delta['content'] != null) {
                final content = delta['content'] as String;
                buffer.write(content);
                onProgress(content);
              }
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    }

    return buffer.toString();
  }
}
